mysqldump: [Warning] Using a password on the command line interface can be insecure.
-- My<PERSON><PERSON> dump 10.13  Distrib 8.0.35, for Linux (x86_64)
--
-- Host: localhost    Database: msc-app-ts
-- ------------------------------------------------------
-- Server version	8.0.35

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `Beneficiary`
--

DROP TABLE IF EXISTS `Beneficiary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Beneficiary` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `kinship` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `birthday` datetime NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `gender` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `education` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `photo` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `curp` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_FC23CBBDA76ED395` (`user_id`),
  CONSTRAINT `FK_FC23CBBDA76ED395` FOREIGN KEY (`user_id`) REFERENCES `User` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Beneficiary`
--

LOCK TABLES `Beneficiary` WRITE;
/*!40000 ALTER TABLE `Beneficiary` DISABLE KEYS */;
INSERT INTO `Beneficiary` VALUES (1,4,'ALONDRA','GARCIA','Cónyuge','2005-03-02 00:00:00','1','Femenino','Primaria','users/4/beneficiaries/1/6859b8371c42f.png','2025-06-23 20:25:27','2025-07-12 22:33:33','CEAN041205MDFLLLA2'),(2,4,'Neli','cea','Mamá','1977-06-19 00:00:00','2','FEMENINO','Uni',NULL,'2025-06-23 21:13:47','2025-06-23 21:28:39','GGUJNBGTJKKL'),(3,4,'ximena','celis ','Mamá ','1990-06-23 00:00:00','1','FEMENINO','Esa',NULL,'2025-06-23 21:22:57','2025-06-23 21:31:50','NDNDJJDNE'),(4,1,'Lenin','Cervantes Gonzalez ','HIJO(A)','2025-07-10 00:00:00','1','MASCULINO','SECUNDARIA INCOMPLETA','users/1/beneficiaries/4/file_1752507087.jpg','2025-07-10 02:17:27','2025-07-14 15:31:27','CEGL000716HDFRNNA0'),(5,1,'Yes','Paternl Matermo','HERMANO(A)','2025-07-10 00:00:00','1','FEMENINO','SECUNDARIA COMPLETA','users/1/beneficiaries/5/file_1752358510.jpg','2025-07-12 22:15:10','2025-07-12 22:33:33','CRGL000716HDFRNNA1'),(6,1,'Lenon','Si Si','HIJO(A)','2025-07-11 00:00:00','1','FEMENINO','PREPARATORIA INCOMPLETA','users/1/beneficiaries/6/file_1752360794.jpg','2025-07-12 22:53:14','2025-07-12 22:53:14','CEGL000716HDFRNNA6'),(7,1,'Len','Len Len','HERMANO(A)','2025-07-01 00:00:00','1','MASCULINO','LICENCIATURA COMPLETA','users/1/beneficiaries/7/file_1752363982.jpg','2025-07-12 23:41:42','2025-07-12 23:46:22','CEGL000716HDFRNNA8');
/*!40000 ALTER TABLE `Beneficiary` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Benefit`
--

DROP TABLE IF EXISTS `Benefit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Benefit` (
  `id` int NOT NULL AUTO_INCREMENT,
  `validity_start_date` datetime NOT NULL,
  `validity_end_date` datetime NOT NULL,
  `title` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Benefit`
--

LOCK TABLES `Benefit` WRITE;
/*!40000 ALTER TABLE `Benefit` DISABLE KEYS */;
INSERT INTO `Benefit` VALUES (1,'2025-06-16 21:38:38','2025-06-16 21:38:38','TITULO','ASD','ASDASDASDASD','0','2025-06-16 21:38:38','2025-06-16 21:49:42'),(2,'2025-06-16 15:49:00','2025-08-16 15:49:00','LANDA','benefit/6850916f37375.jpg','i m u','0','2025-06-16 21:49:35','2025-07-10 18:38:16'),(3,'2025-06-05 14:15:00','2025-06-27 14:15:00','ADIDAS','benefit/686c523ad7f1a.png','15% DE DESCUENTO','0','2025-06-23 20:17:29','2025-07-10 17:19:54'),(4,'2025-06-23 14:18:00','2025-06-23 14:18:00','ADIDAS','benefit/686c47ea9b1f5.png','15% menos en toda la tienda','0','2025-06-23 20:18:54','2025-07-10 17:20:24'),(5,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','0','2025-06-23 20:21:29','2025-07-10 17:19:58'),(6,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','0','2025-06-23 20:21:29','2025-07-10 17:20:27'),(7,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','0','2025-06-23 20:21:29','2025-07-10 17:20:19'),(8,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','0','2025-06-23 20:21:29','2025-07-10 17:20:21'),(9,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','0','2025-06-23 20:21:29','2025-07-10 17:20:16'),(10,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','0','2025-06-23 20:21:29','2025-07-10 17:20:12'),(11,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','0','2025-06-23 20:21:29','2025-07-10 17:20:14'),(12,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','1','2025-06-23 20:21:29','2025-07-07 22:19:11'),(13,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','0','2025-06-23 20:21:29','2025-07-10 17:20:03'),(14,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','0','2025-06-23 20:21:29','2025-07-10 17:20:06'),(15,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','0','2025-06-23 20:21:29','2025-07-10 17:20:00'),(16,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','0','2025-06-23 20:21:29','2025-07-10 17:19:47'),(17,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','0','2025-06-23 20:21:29','2025-07-10 17:19:42'),(18,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','0','2025-06-23 20:21:29','2025-07-10 17:19:44'),(19,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','0','2025-06-23 20:21:29','2025-07-10 17:20:08'),(20,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','0','2025-06-23 20:21:29','2025-07-10 17:19:50'),(21,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','1','2025-06-23 20:21:29','2025-07-07 22:19:11'),(22,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','1','2025-06-23 20:21:29','2025-07-07 22:19:11'),(23,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','1','2025-06-23 20:21:29','2025-07-07 22:19:11'),(24,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','1','2025-06-23 20:21:29','2025-07-07 22:19:11'),(25,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','1','2025-06-23 20:21:29','2025-07-07 22:19:11'),(26,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','1','2025-06-23 20:21:29','2025-07-07 22:19:11'),(27,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','1','2025-06-23 20:21:29','2025-07-07 22:19:11'),(28,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','1','2025-06-23 20:21:29','2025-07-07 22:19:11'),(29,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','1','2025-06-23 20:21:29','2025-07-07 22:19:11'),(30,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','1','2025-06-23 20:21:29','2025-07-07 22:19:11'),(31,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','1','2025-06-23 20:21:29','2025-07-07 22:19:11'),(32,'2025-06-23 14:21:00','2025-06-23 14:21:00','ADIDAD','benefit/686c47de97081.png','15% DE DESCUENTO EN TODA LA','1','2025-06-23 20:21:29','2025-07-07 22:19:11'),(33,'2025-07-01 12:38:00','2025-07-31 12:38:00','GON','benefit/687008bfe7ec9.jpg','Sí','0','2025-07-10 18:38:56','2025-07-11 20:04:16'),(34,'2025-07-01 14:04:00','2025-07-12 14:04:00','LANDA','benefit/68716f7923f2e.jpg','1','1','2025-07-11 20:04:33','2025-07-11 20:09:29');
/*!40000 ALTER TABLE `Benefit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Company`
--

DROP TABLE IF EXISTS `Company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Company` (
  `id` int NOT NULL AUTO_INCREMENT,
  `region_id` int DEFAULT NULL,
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `updated_at` datetime NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_800230D398260155` (`region_id`),
  CONSTRAINT `FK_800230D398260155` FOREIGN KEY (`region_id`) REFERENCES `Region` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Company`
--

LOCK TABLES `Company` WRITE;
/*!40000 ALTER TABLE `Company` DISABLE KEYS */;
INSERT INTO `Company` VALUES (1,2,'SAIYAYIN','1','2025-06-12 22:31:32','2025-06-12 22:31:32'),(2,2,'LENONCINO','1','2025-06-17 17:23:29','2025-06-17 17:23:29'),(3,2,'MASOFTCODE','1','2025-07-07 22:24:28','2025-06-23 20:15:07'),(4,2,'Grupo Optimo','1','2025-07-07 22:29:53','2025-07-07 22:29:53'),(5,3,'sa de sv','1','2025-07-07 22:57:13','2025-07-07 22:57:13');
/*!40000 ALTER TABLE `Company` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Conversation`
--

DROP TABLE IF EXISTS `Conversation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Conversation` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `status` varchar(1) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_92BFFD11979B1AD6` (`company_id`),
  CONSTRAINT `FK_92BFFD11979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `Company` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Conversation`
--

LOCK TABLES `Conversation` WRITE;
/*!40000 ALTER TABLE `Conversation` DISABLE KEYS */;
INSERT INTO `Conversation` VALUES (1,2,'1','2025-06-30 17:16:40'),(3,2,'1','2025-07-03 18:46:18'),(6,2,'1','2025-07-06 01:29:09');
/*!40000 ALTER TABLE `Conversation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `DeviceToken`
--

DROP TABLE IF EXISTS `DeviceToken`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `DeviceToken` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_CC76A4C9A76ED395` (`user_id`),
  CONSTRAINT `FK_CC76A4C9A76ED395` FOREIGN KEY (`user_id`) REFERENCES `User` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `DeviceToken`
--

LOCK TABLES `DeviceToken` WRITE;
/*!40000 ALTER TABLE `DeviceToken` DISABLE KEYS */;
INSERT INTO `DeviceToken` VALUES (1,1,'2025-06-17 17:36:37','2025-06-17 17:36:37','ExponentPushToken[5XwmeaG_pKhGrQqR7Tx1NB]'),(2,1,'2025-06-18 23:41:39','2025-06-18 23:41:39','ExponentPushToken[eeNsLyDopR81pjjmnlKI39]'),(3,2,'2025-06-18 23:47:13','2025-06-18 23:47:13','ExponentPushToken[eeNsLyDopR81pjjmnlKI39]'),(4,1,'2025-07-10 03:10:58','2025-07-10 03:10:58','ExponentPushToken[9UY6z7Lk6W7jwskjIy4djY]'),(5,1,'2025-07-11 20:01:23','2025-07-11 20:01:23','ExponentPushToken[XCHghPJnIudln8fqXaZWbB]'),(6,1,'2025-07-12 00:39:18','2025-07-12 00:39:18','ExponentPushToken[t_k6UCDa0ZPOp2aAwaecOR]');
/*!40000 ALTER TABLE `DeviceToken` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Event`
--

DROP TABLE IF EXISTS `Event`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Event` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `description` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Event`
--

LOCK TABLES `Event` WRITE;
/*!40000 ALTER TABLE `Event` DISABLE KEYS */;
INSERT INTO `Event` VALUES (1,'LENON','2025-06-13 22:56:12','2025-06-13 22:56:12','adasdasdasdasd','2025-06-13 16:40:00','2025-07-12 16:40:00','0','event/684cac8c8f69a.jpg'),(2,'LENON','2025-06-13 22:56:16','2025-06-13 22:56:16','adasdasdasdasd','2025-06-13 16:40:00','2025-07-12 16:40:00','0','event/684cac9000d03.jpg'),(3,'LENON','2025-06-13 22:58:34','2025-06-13 22:58:34','asdasd','2025-06-13 16:58:00','2025-07-12 16:58:00','0','event/684cad1a6ccbd.jpg'),(4,'LENON','2025-06-13 23:01:53','2025-06-13 23:01:53','asdasdasdasd','2025-06-12 17:01:00','2025-06-12 17:01:00','0','event/684cade13c3e3.jpg'),(5,'asd','2025-06-13 23:29:51','2025-06-13 23:29:51','asdasdasdasd','2025-06-20 17:29:00','2025-07-31 17:29:00','1','event/684cb46fb2c1d.jpg'),(6,'MI CUMPLE','2025-06-23 20:27:52','2025-06-23 20:27:52','HOY TODO ES SOLEDAD NOSE SI VUELVA A VERTER DESPUES','2025-06-19 14:27:00','2025-07-25 14:27:00','1','event/6859b8c8d34dc.png');
/*!40000 ALTER TABLE `Event` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `FormEntry`
--

DROP TABLE IF EXISTS `FormEntry`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `FormEntry` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `event_id` int DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `formTemplate_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_A11544DBE9CA3C83` (`formTemplate_id`),
  KEY `IDX_A11544DBA76ED395` (`user_id`),
  KEY `IDX_A11544DB71F7E88B` (`event_id`),
  CONSTRAINT `FK_A11544DB71F7E88B` FOREIGN KEY (`event_id`) REFERENCES `Event` (`id`),
  CONSTRAINT `FK_A11544DBA76ED395` FOREIGN KEY (`user_id`) REFERENCES `User` (`id`),
  CONSTRAINT `FK_A11544DBE9CA3C83` FOREIGN KEY (`formTemplate_id`) REFERENCES `FormTemplate` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `FormEntry`
--

LOCK TABLES `FormEntry` WRITE;
/*!40000 ALTER TABLE `FormEntry` DISABLE KEYS */;
INSERT INTO `FormEntry` VALUES (1,1,NULL,'2025-07-10 01:04:23','2025-07-10 01:04:23','1',2),(2,1,NULL,'2025-07-10 01:05:53','2025-07-10 01:05:53','1',1),(3,1,NULL,'2025-07-10 01:08:58','2025-07-10 01:08:58','1',4),(4,1,NULL,'2025-07-10 23:09:41','2025-07-10 23:09:41','1',6),(5,1,NULL,'2025-07-11 00:36:16','2025-07-11 00:36:16','0',7),(6,1,NULL,'2025-07-11 18:18:38','2025-07-11 18:18:38','1',8),(7,1,NULL,'2025-07-11 19:18:07','2025-07-11 19:18:07','0',10),(8,1,NULL,'2025-07-11 20:12:14','2025-07-11 20:12:14','1',10),(9,1,NULL,'2025-07-11 21:04:52','2025-07-11 21:04:52','1',11);
/*!40000 ALTER TABLE `FormEntry` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `FormEntryValue`
--

DROP TABLE IF EXISTS `FormEntryValue`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `FormEntryValue` (
  `id` int NOT NULL AUTO_INCREMENT,
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `formEntry_id` int DEFAULT NULL,
  `formTemplateField_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_9B141A21664AA52` (`formEntry_id`),
  KEY `IDX_9B141A2140333EEA` (`formTemplateField_id`),
  CONSTRAINT `FK_9B141A2140333EEA` FOREIGN KEY (`formTemplateField_id`) REFERENCES `FormTemplateField` (`id`),
  CONSTRAINT `FK_9B141A21664AA52` FOREIGN KEY (`formEntry_id`) REFERENCES `FormEntry` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `FormEntryValue`
--

LOCK TABLES `FormEntryValue` WRITE;
/*!40000 ALTER TABLE `FormEntryValue` DISABLE KEYS */;
INSERT INTO `FormEntryValue` VALUES (1,'Este es un texto largo de prueba para el campo Nombre Completo.\nIncluye múltiples líneas\ny caracteres especiales: áéíóú ñ','1',1,2),(2,'Leninismo1','1',2,1),(3,'Tallagrandota','1',3,3),(4,'Medianasan','1',3,4),(5,'azul','1',3,5),(6,'Wewewe','1',3,6),(7,'[\"hola\"]','1',3,7),(8,'54','1',3,8),(9,'Wewewe','1',3,9),(10,'Wewewe','1',3,10),(11,'[\"wolverine\"]','1',5,12),(12,'{\"file_path\":\"\\/1\\/8\\/6871557ee9991_1752257918.jpeg\",\"original_name\":\"429cd98c-91ac-438b-b837-d8c9c9d52195.jpeg\",\"file_name\":\"6871557ee9991_1752257918.jpeg\",\"file_size\":263171,\"mime_type\":\"image\\/jpeg\",\"uploaded_at\":\"2025-07-11 18:18:38\",\"user_id\":1,\"form_template_id\":8,\"migrated_at\":\"2025-07-11 21:02:06\"}','1',6,13),(13,'','1',7,14),(14,'content://com.android.providers.media.documents/document/document%3A1000003556','1',8,14),(15,'content://com.android.providers.media.documents/document/document%3A1000003556','1',9,15);
/*!40000 ALTER TABLE `FormEntryValue` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `FormTemplate`
--

DROP TABLE IF EXISTS `FormTemplate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `FormTemplate` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tenant` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `FormTemplate`
--

LOCK TABLES `FormTemplate` WRITE;
/*!40000 ALTER TABLE `FormTemplate` DISABLE KEYS */;
INSERT INTO `FormTemplate` VALUES (1,'REGISTROS','CREAR USUARIOS ','2025-06-24 22:09:46','2025-07-10 19:34:32','1','ts'),(2,'UNIFORMES','Se les entregará uniformes a los estudiantes que cursan del 1ero a 6to año.','2025-06-26 22:53:42','2025-07-11 00:09:16','0','ts'),(3,'redes socialres','','2025-06-26 23:50:51','2025-07-11 00:09:33','0','ts'),(4,'test','description','2025-06-27 00:01:59','2025-07-11 18:10:41','1','ts'),(5,'UNIFORMES','asdasd','2025-07-09 17:19:26','2025-07-11 00:09:35','0','ts'),(6,'Lenin','1','2025-07-10 22:11:48','2025-07-10 22:30:48','1','ts'),(7,'MARVEL','SÚPER HEROE FAVORITO','2025-07-11 00:12:10','2025-07-11 18:11:21','1','ts'),(8,'IMAGEN','IMAGEN','2025-07-11 01:11:13','2025-07-11 18:18:06','1','ts'),(9,'DCA','DC','2025-07-11 17:12:10','2025-07-11 18:10:18','1','ts'),(10,'SÚPER HEROÉS DC','SÚPER HEROÉS DC','2025-07-11 18:58:01','2025-07-11 18:58:18','1','ts'),(11,'Formulario 1','1','2025-07-11 21:03:35','2025-07-11 21:04:01','1','ts');
/*!40000 ALTER TABLE `FormTemplate` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `FormTemplateField`
--

DROP TABLE IF EXISTS `FormTemplateField`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `FormTemplateField` (
  `id` int NOT NULL AUTO_INCREMENT,
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `isRequired` tinyint(1) NOT NULL,
  `options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `position` int DEFAULT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `help` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `multiple` tinyint(1) DEFAULT NULL,
  `cols` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `textareaCols` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `formTemplate_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_BF1E60EEE9CA3C83` (`formTemplate_id`),
  CONSTRAINT `FK_BF1E60EEE9CA3C83` FOREIGN KEY (`formTemplate_id`) REFERENCES `FormTemplate` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `FormTemplateField`
--

LOCK TABLES `FormTemplateField` WRITE;
/*!40000 ALTER TABLE `FormTemplateField` DISABLE KEYS */;
INSERT INTO `FormTemplateField` VALUES (1,'Nombre','Nombre Completo','textarea',1,'',1,'1','Nombre Completo',0,'col-md-6','3',1),(2,'Nombre Alumno','Nombre Completo','textarea',1,'',1,'1','Nombre Completo del Alumno',0,'col-md-12','3',2),(3,'talla de zapato','tallazapato','textarea',1,'',1,'1','no hay números medios',0,'','3',4),(4,'talla de playera','talla-playera','textarea',1,'',2,'1','No hay cambios',0,'col-md-6','3',4),(5,'Color de uniforme','color','radio',0,'azul,rosa, rojo,gray',3,'1','médicos siempre es blanco',0,'',NULL,4),(6,'wewewe','DEmoo','textarea',0,NULL,4,'1','ti',0,NULL,'3',4),(7,'wewewe','doos','checkbox',0,'hola,\r\nadios,\r\nhola,',5,'1','ti',1,'col-md-6',NULL,4),(8,'wewewe','tres','radio',0,'3,\r\n54,\r\n6,',6,'1','ti',0,NULL,NULL,4),(9,'wewewe','cuatro','textarea',0,NULL,7,'1',NULL,0,NULL,'3',4),(10,'wewewe','cinoc','textarea',0,NULL,8,'1','ti',0,NULL,'3',4),(11,'PODER','PODER','file',0,NULL,1,'1','DIme tu poder',0,NULL,'3',6),(12,'Wolverine vs Cyclops','wolverinevsCyclops','checkbox',0,'wolverine,cyclops',1,'1','wolverinevsCyclops',0,'col-md-3','3',7),(13,'IMAGEN','IMAGEN','file',0,NULL,1,'1','IMAGEN',0,'col-md-3','3',8),(14,'IMAGEN','IMAGEN','file',0,NULL,1,'1','SÚPER HEROÉS DC',0,'col-md-6','3',10),(15,'Wolverine vs Cyclops','UNIFORMES','file',0,NULL,1,'1','no hay números medios',0,'col-md-4','3',11);
/*!40000 ALTER TABLE `FormTemplateField` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `History`
--

DROP TABLE IF EXISTS `History`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `History` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `created_at` datetime NOT NULL,
  `eventType_id` int NOT NULL,
  `action` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_E80749D7A76ED395` (`user_id`),
  KEY `IDX_E80749D7C15B25DE` (`eventType_id`),
  CONSTRAINT `FK_E80749D7A76ED395` FOREIGN KEY (`user_id`) REFERENCES `User` (`id`),
  CONSTRAINT `FK_E80749D7C15B25DE` FOREIGN KEY (`eventType_id`) REFERENCES `HistoryEvents` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `History`
--

LOCK TABLES `History` WRITE;
/*!40000 ALTER TABLE `History` DISABLE KEYS */;
/*!40000 ALTER TABLE `History` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `HistoryEvents`
--

DROP TABLE IF EXISTS `HistoryEvents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `HistoryEvents` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `HistoryEvents`
--

LOCK TABLES `HistoryEvents` WRITE;
/*!40000 ALTER TABLE `HistoryEvents` DISABLE KEYS */;
/*!40000 ALTER TABLE `HistoryEvents` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Message`
--

DROP TABLE IF EXISTS `Message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Message` (
  `id` int NOT NULL AUTO_INCREMENT,
  `author_id` int NOT NULL,
  `conversation_id` int NOT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_790009E3F675F31B` (`author_id`),
  KEY `IDX_790009E39AC0396` (`conversation_id`),
  CONSTRAINT `FK_790009E39AC0396` FOREIGN KEY (`conversation_id`) REFERENCES `Conversation` (`id`),
  CONSTRAINT `FK_790009E3F675F31B` FOREIGN KEY (`author_id`) REFERENCES `User` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=123 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Message`
--

LOCK TABLES `Message` WRITE;
/*!40000 ALTER TABLE `Message` DISABLE KEYS */;
INSERT INTO `Message` VALUES (1,9,1,'Hola, help','2025-06-30 17:29:51'),(54,9,1,'ww','2025-06-30 23:34:09'),(55,9,1,'hola','2025-07-02 17:52:43'),(115,8,6,'Hola','2025-07-06 01:29:32'),(116,8,6,'Uwu','2025-07-06 01:29:43'),(117,9,6,'en q te puedo ayudar¿','2025-07-06 01:29:57'),(118,9,6,'¿¿¿','2025-07-06 01:30:09'),(119,9,6,'Hola','2025-07-07 15:22:31'),(120,9,6,'hola leni','2025-07-07 15:23:32'),(121,8,6,'.','2025-07-07 15:28:52'),(122,9,3,'Hola','2025-07-07 23:29:05');
/*!40000 ALTER TABLE `Message` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Notification`
--

DROP TABLE IF EXISTS `Notification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Notification` (
  `id` int NOT NULL AUTO_INCREMENT,
  `message` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `sent_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Notification`
--

LOCK TABLES `Notification` WRITE;
/*!40000 ALTER TABLE `Notification` DISABLE KEYS */;
INSERT INTO `Notification` VALUES (1,'Atencion los dias 15 y 16 \r\ngracias','DIA FESTIVO','2025-06-23 20:19:40','2025-06-23 20:19:40','1',NULL),(2,'AVISO EL DIA 15 Y 17 \r\nGRACIAS','DIA FESTIVO','2025-06-23 20:22:08','2025-06-23 20:22:08','1',NULL),(3,'JKSAKDJAJSDLKAJD','DIA FESTIVO','2025-06-23 20:25:58','2025-06-23 20:25:58','1',NULL);
/*!40000 ALTER TABLE `Notification` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `PhoneVerification`
--

DROP TABLE IF EXISTS `PhoneVerification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `PhoneVerification` (
  `id` int NOT NULL AUTO_INCREMENT,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `expiresAt` datetime DEFAULT NULL,
  `verified` tinyint(1) DEFAULT NULL,
  `createdAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `PhoneVerification`
--

LOCK TABLES `PhoneVerification` WRITE;
/*!40000 ALTER TABLE `PhoneVerification` DISABLE KEYS */;
/*!40000 ALTER TABLE `PhoneVerification` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Region`
--

DROP TABLE IF EXISTS `Region`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Region` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_8CEF4405E237E06` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Region`
--

LOCK TABLES `Region` WRITE;
/*!40000 ALTER TABLE `Region` DISABLE KEYS */;
INSERT INTO `Region` VALUES (1,'Lenonicini Empresa','1','2025-06-12 22:31:22','2025-07-07 22:21:41'),(2,'CDMX','1','2025-06-23 20:14:47','2025-06-23 20:14:54'),(3,'CDMX-2','1','2025-07-07 22:56:43','2025-07-07 22:56:43'),(4,'IZTAPALAPA','1','2025-07-10 19:03:21','2025-07-10 19:03:21');
/*!40000 ALTER TABLE `Region` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Role`
--

DROP TABLE IF EXISTS `Role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Role` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Role`
--

LOCK TABLES `Role` WRITE;
/*!40000 ALTER TABLE `Role` DISABLE KEYS */;
INSERT INTO `Role` VALUES (1,'ROLE_USER','2025-06-12 17:48:24','2025-06-12 17:48:24'),(2,'ROLE_ADMIN','2025-06-13 19:51:58','2025-06-13 19:51:58');
/*!40000 ALTER TABLE `Role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SocialMedia`
--

DROP TABLE IF EXISTS `SocialMedia`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `SocialMedia` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `title` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `platform` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_3F45A2A0979B1AD6` (`company_id`),
  CONSTRAINT `FK_3F45A2A0979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `Company` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SocialMedia`
--

LOCK TABLES `SocialMedia` WRITE;
/*!40000 ALTER TABLE `SocialMedia` DISABLE KEYS */;
INSERT INTO `SocialMedia` VALUES (1,1,'asdasd','2025-06-16 21:41:23','2025-07-10 18:32:11','asdasdasdasdasd','2025-06-16 21:41:23','2025-06-16 21:41:23','0','social_media/asd','https://www.facebook.com/photo/?fbid=3152966248197830&set=pcb.3152966424864479&__cft__[0]=AZU_WfT2zdtKKL6w_NrgeNfGEwmUnoIu1N8-','facebook'),(2,1,'1','2025-06-17 00:07:33','2025-07-10 18:32:14','LENON','2025-06-01 18:07:00','2025-06-26 18:07:00','0',NULL,'http://LENON.COM','INSTAGRAM'),(3,2,'asd','2025-06-17 16:42:17','2025-07-10 18:32:16','LENON','2025-06-16 10:42:00','2025-06-25 10:42:00','0','social_media/img20231206wa0009-68519ae95f296.jpg','http://LENON.COM','INSTAGRAM'),(4,1,'asd','2025-06-17 16:43:26','2025-07-10 18:33:22','LENON','2025-06-18 10:43:00','2025-06-24 10:43:00','0','social_media/img20231206wa0009-68519b2e2ce38.jpg','http://LENON.COM','INSTAGRAM'),(5,2,'123456','2025-06-17 17:49:28','2025-07-10 18:36:51','LENON','2025-06-16 11:48:00','2025-07-16 11:48:00','0','social_media/screenshotfrom20250614133113-6851aaa86a9e0.png','http://LENON.COM','YOUTUBE'),(6,2,'asdasd','2025-06-16 21:41:23','2025-07-10 18:36:56','asdasdasdasdasd','2025-06-16 21:41:00','2025-07-15 21:41:00','0','social_media/asd','https://www.facebook.com/photo/?fbid=3152966248197830&set=pcb.3152966424864479&__cft__[0]=AZU_WfT2zdtKKL6w_NrgeNfGEwmUnoIu1N8-','facebook'),(7,2,'1','2025-06-17 00:07:33','2025-07-10 18:36:53','LENON','2025-06-01 18:07:00','2025-07-14 18:07:00','0',NULL,'http://LENON.COM','INSTAGRAM'),(8,3,'YES','2025-07-10 18:37:42','2025-07-11 20:03:48','LENON','2025-07-01 12:37:00','2025-07-10 12:37:00','1','social_media/img20231206wa0009-687008764046b.jpg','https://www.facebook.com/photo/?fbid=3152966248197830&set=pcb.3152966424864479','Facebook');
/*!40000 ALTER TABLE `SocialMedia` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `UnreadMessage`
--

DROP TABLE IF EXISTS `UnreadMessage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `UnreadMessage` (
  `id` int NOT NULL AUTO_INCREMENT,
  `conversation_id` int NOT NULL,
  `message_id` int NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_B0E7B4CB537A1329` (`message_id`),
  KEY `IDX_B0E7B4CB9AC0396` (`conversation_id`),
  CONSTRAINT `FK_B0E7B4CB537A1329` FOREIGN KEY (`message_id`) REFERENCES `Message` (`id`),
  CONSTRAINT `FK_B0E7B4CB9AC0396` FOREIGN KEY (`conversation_id`) REFERENCES `Conversation` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `UnreadMessage`
--

LOCK TABLES `UnreadMessage` WRITE;
/*!40000 ALTER TABLE `UnreadMessage` DISABLE KEYS */;
/*!40000 ALTER TABLE `UnreadMessage` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `User`
--

DROP TABLE IF EXISTS `User`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `User` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` int DEFAULT NULL,
  `role_id` int NOT NULL,
  `name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `google_auth` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `curp` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_seen` datetime DEFAULT NULL,
  `birthday` datetime DEFAULT NULL,
  `photo` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone_number` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `gender` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `education` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `verified` tinyint(1) DEFAULT NULL,
  `verification_code` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `employee_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_2DA17977979B1AD6` (`company_id`),
  KEY `IDX_2DA17977D60322AC` (`role_id`),
  CONSTRAINT `FK_2DA17977979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `Company` (`id`),
  CONSTRAINT `FK_2DA17977D60322AC` FOREIGN KEY (`role_id`) REFERENCES `Role` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `User`
--

LOCK TABLES `User` WRITE;
/*!40000 ALTER TABLE `User` DISABLE KEYS */;
INSERT INTO `User` VALUES (1,3,2,'Jose Lenin PRODUCTION','$2y$13$9HqDMJ.A9mwpUwV/uVgbk.PsXIhad7LyKl27I/Bqo7HTnBqG8d/ni','Cervantes González','1',NULL,'CEGL000716HDFRNNA6','2025-07-14 15:31:27','2025-06-12 00:00:00','profile/687144134f9c3.jpg','5610676487','<EMAIL>','2025-06-12 17:48:24','2025-07-14 15:30:26','Femenino','Posgrado',1,'378809','034'),(2,3,1,'Jose Lenin','$2y$13$ciOzW3qVDg6wo2aFIVpGjeW/5frbAhGLin3o/lrTqaSEwmVoPMRCy','Cervantes Gonzalez','1',NULL,'CEGL000716HDRNNA7','2025-06-19 00:57:36','2025-06-13 00:00:00','profile/68535e191baa3.jpg','5610676487','<EMAIL>','2025-06-13 20:05:13','2025-06-19 00:47:21','Masculino','Posgrado',1,NULL,'0034'),(4,3,1,'ALONSO','$2y$13$45PRD6iPxJ.lLn9tgWDNOulY/BhfUbmcP2rrYVxEEzTyeZJx.RVT.','TORRES','1',NULL,'CEAN041205MDFLLLA1','2025-06-23 21:32:14','2004-12-05 00:00:00','profile/6859c5714f15a.jpg','5513015173','<EMAIL>','2025-06-23 20:24:02','2025-07-04 23:14:33','Masculino','Universidad',0,'939546','0055'),(5,3,2,'Admin','$2y$13$HOGvxSqGxBN8L.XJ7ADMkO6oKRzn0KRGT9/Qw4fg.bYZLHp5VPuSi','User','1',NULL,NULL,NULL,NULL,NULL,NULL,'<EMAIL>','2025-06-27 17:06:00','2025-06-27 17:06:00',NULL,NULL,1,NULL,NULL),(8,3,1,'ximena','$2y$13$OrsnKmTq4dBuxHCs446Jjep9ygiOxuCMzIWSjpsTzcsd8aFmgkdDi','celis','1',NULL,'CEGL000716HDFRN','2025-07-07 23:29:32','2025-06-18 00:00:00',NULL,'5513015173','<EMAIL>','2025-06-28 01:42:27','2025-06-28 01:42:27','Masculino','Secundaria',1,NULL,'12'),(9,3,2,'Erick Jesus','$2y$13$LDsrVL0NZqnXibhgALHu7OWRJaizHz9pEZ0C1k/tyIPWJCrNjTm3O','Cervantes González','1',NULL,NULL,'2025-07-08 01:24:25','2025-06-11 00:00:00',NULL,'5610676487','<EMAIL>','2025-06-28 01:44:36','2025-07-08 01:24:17','Femenino','Universidad',0,'688748','12'),(10,3,2,'Jose Antonio','$2y$13$nL/virAxzN50DA6vIDtHi.G14J73kNS3la1bWWCdR7UKuBvtXemwO','Muñoz Alonzo','1',NULL,'CEGL000716HDFRNNA7','2025-07-08 19:56:18','2025-07-08 00:00:00',NULL,'5610676487','<EMAIL>','2025-07-08 16:08:05','2025-07-08 19:56:18','Masculino','Posgrado',0,'867842','001'),(11,3,2,'Dulce Jimena','$2y$13$FLjNi6m.tPJZvb8qpkWF6usXwQ0b4o1dxYtVrNrIjo3hyIzGAQmri','Lopez Posadas','1',NULL,'LOPD031003MDFPSLA2','2025-07-08 20:59:30','2025-07-08 00:00:00',NULL,'5549653881','<EMAIL>','2025-07-08 20:42:19','2025-07-08 20:55:08','Femenino','Universidad',1,NULL,'002'),(12,3,2,'11','$2y$13$YudX2cbFPg1vNhB4J23Xl.7Ebzch/DeeAKPywOLdp/7BSLtuVhDES','1','1',NULL,'CEAN041205MDFLLLA1',NULL,'2025-07-08 00:00:00','profile/file_1752365626.jpg','1','<EMAIL>','2025-07-13 00:13:46','2025-07-13 00:13:46','Masculino','Preparatoria',NULL,NULL,'002');
/*!40000 ALTER TABLE `User` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `benefit_company`
--

DROP TABLE IF EXISTS `benefit_company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `benefit_company` (
  `benefit_id` int NOT NULL,
  `company_id` int NOT NULL,
  PRIMARY KEY (`benefit_id`,`company_id`),
  KEY `IDX_17F80D64B517B89` (`benefit_id`),
  KEY `IDX_17F80D64979B1AD6` (`company_id`),
  CONSTRAINT `FK_17F80D64979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `Company` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_17F80D64B517B89` FOREIGN KEY (`benefit_id`) REFERENCES `Benefit` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `benefit_company`
--

LOCK TABLES `benefit_company` WRITE;
/*!40000 ALTER TABLE `benefit_company` DISABLE KEYS */;
INSERT INTO `benefit_company` VALUES (1,1),(1,2),(2,1),(2,2),(3,2),(3,3),(4,2),(4,3),(5,3),(6,3),(7,3),(8,3),(9,3),(10,3),(11,3),(13,3),(14,3),(15,3),(16,3),(17,3),(18,3),(19,3),(20,3),(33,1),(33,2),(33,3),(33,4),(33,5),(34,1),(34,2),(34,3),(34,4);
/*!40000 ALTER TABLE `benefit_company` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `conversation_user`
--

DROP TABLE IF EXISTS `conversation_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `conversation_user` (
  `conversation_id` int NOT NULL,
  `user_id` int NOT NULL,
  PRIMARY KEY (`conversation_id`,`user_id`),
  KEY `IDX_5AECB5559AC0396` (`conversation_id`),
  KEY `IDX_5AECB555A76ED395` (`user_id`),
  CONSTRAINT `FK_5AECB5559AC0396` FOREIGN KEY (`conversation_id`) REFERENCES `Conversation` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_5AECB555A76ED395` FOREIGN KEY (`user_id`) REFERENCES `User` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `conversation_user`
--

LOCK TABLES `conversation_user` WRITE;
/*!40000 ALTER TABLE `conversation_user` DISABLE KEYS */;
INSERT INTO `conversation_user` VALUES (3,9),(6,8);
/*!40000 ALTER TABLE `conversation_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `doctrine_migration_versions`
--

DROP TABLE IF EXISTS `doctrine_migration_versions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `doctrine_migration_versions` (
  `version` varchar(191) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `executed_at` datetime DEFAULT NULL,
  `execution_time` int DEFAULT NULL,
  PRIMARY KEY (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `doctrine_migration_versions`
--

LOCK TABLES `doctrine_migration_versions` WRITE;
/*!40000 ALTER TABLE `doctrine_migration_versions` DISABLE KEYS */;
INSERT INTO `doctrine_migration_versions` VALUES ('DoctrineMigrations\\Version20250616173840','2025-06-16 17:38:50',13),('DoctrineMigrations\\Version20250616175329','2025-06-16 17:53:58',12),('DoctrineMigrations\\Version20250616175754','2025-06-16 17:57:58',27),('DoctrineMigrations\\Version20250620230909','2025-06-24 19:53:05',27),('DoctrineMigrations\\Version20250624195336','2025-06-24 19:53:40',333),('DoctrineMigrations\\Version20250627155730','2025-06-27 15:57:59',18),('DoctrineMigrations\\Version20250630171054','2025-06-30 17:11:40',226),('DoctrineMigrations\\Version20250704212940',NULL,NULL),('DoctrineMigrations\\Version20250708182153','2025-07-10 16:14:38',48),('DoctrineMigrations\\Version20250709104615',NULL,NULL),('DoctrineMigrations\\Version20250709165838','2025-07-10 16:14:38',8),('DoctrineMigrations\\Version20250709170216','2025-07-10 16:14:38',75),('DoctrineMigrations\\Version20250709175932','2025-07-09 17:59:41',418);
/*!40000 ALTER TABLE `doctrine_migration_versions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `event_company`
--

DROP TABLE IF EXISTS `event_company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `event_company` (
  `event_id` int NOT NULL,
  `company_id` int NOT NULL,
  PRIMARY KEY (`event_id`,`company_id`),
  KEY `IDX_CAE8A0E071F7E88B` (`event_id`),
  KEY `IDX_CAE8A0E0979B1AD6` (`company_id`),
  CONSTRAINT `FK_CAE8A0E071F7E88B` FOREIGN KEY (`event_id`) REFERENCES `Event` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_CAE8A0E0979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `Company` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `event_company`
--

LOCK TABLES `event_company` WRITE;
/*!40000 ALTER TABLE `event_company` DISABLE KEYS */;
INSERT INTO `event_company` VALUES (1,1),(1,2),(3,1),(3,2),(4,1),(4,2),(5,1),(5,2),(6,3);
/*!40000 ALTER TABLE `event_company` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `form_template_company`
--

DROP TABLE IF EXISTS `form_template_company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `form_template_company` (
  `formtemplate_id` int NOT NULL,
  `company_id` int NOT NULL,
  PRIMARY KEY (`formtemplate_id`,`company_id`),
  KEY `IDX_E3B87E5B979B1AD6` (`company_id`),
  KEY `IDX_E3B87E5B6F56B354` (`formtemplate_id`),
  CONSTRAINT `FK_E3B87E5B6F56B354` FOREIGN KEY (`formtemplate_id`) REFERENCES `FormTemplate` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_FTC_COMPANY_ID` FOREIGN KEY (`company_id`) REFERENCES `Company` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `form_template_company`
--

LOCK TABLES `form_template_company` WRITE;
/*!40000 ALTER TABLE `form_template_company` DISABLE KEYS */;
INSERT INTO `form_template_company` VALUES (4,2),(7,2),(8,2),(1,3),(7,3),(8,3),(6,4),(9,5);
/*!40000 ALTER TABLE `form_template_company` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `lock_keys`
--

DROP TABLE IF EXISTS `lock_keys`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lock_keys` (
  `key_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key_token` varchar(44) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key_expiration` int unsigned NOT NULL,
  PRIMARY KEY (`key_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `lock_keys`
--

LOCK TABLES `lock_keys` WRITE;
/*!40000 ALTER TABLE `lock_keys` DISABLE KEYS */;
/*!40000 ALTER TABLE `lock_keys` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notification_company`
--

DROP TABLE IF EXISTS `notification_company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notification_company` (
  `notification_id` int NOT NULL,
  `company_id` int NOT NULL,
  PRIMARY KEY (`notification_id`,`company_id`),
  KEY `IDX_5AA183F8EF1A9D84` (`notification_id`),
  KEY `IDX_5AA183F8979B1AD6` (`company_id`),
  CONSTRAINT `FK_5AA183F8979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `Company` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_5AA183F8EF1A9D84` FOREIGN KEY (`notification_id`) REFERENCES `Notification` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notification_company`
--

LOCK TABLES `notification_company` WRITE;
/*!40000 ALTER TABLE `notification_company` DISABLE KEYS */;
INSERT INTO `notification_company` VALUES (1,3),(2,3),(3,1),(3,2),(3,3);
/*!40000 ALTER TABLE `notification_company` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `region_user`
--

DROP TABLE IF EXISTS `region_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `region_user` (
  `user_id` int NOT NULL,
  `region_id` int NOT NULL,
  PRIMARY KEY (`user_id`,`region_id`),
  KEY `IDX_5435C17EA76ED395` (`user_id`),
  KEY `IDX_5435C17E98260155` (`region_id`),
  CONSTRAINT `FK_5435C17E98260155` FOREIGN KEY (`region_id`) REFERENCES `Region` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_5435C17EA76ED395` FOREIGN KEY (`user_id`) REFERENCES `User` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `region_user`
--

LOCK TABLES `region_user` WRITE;
/*!40000 ALTER TABLE `region_user` DISABLE KEYS */;
INSERT INTO `region_user` VALUES (1,1),(1,2),(8,2),(9,2),(10,1),(10,2),(10,3),(11,1),(11,2),(11,3),(12,1),(12,2),(12,3),(12,4);
/*!40000 ALTER TABLE `region_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping routines for database 'msc-app-ts'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-14 21:48:43
