<?php

namespace App\Controller;

use App\Entity\App\Role;
use App\Entity\App\User;
use App\Enum\Status;
use App\Form\UserType;
use App\Repository\CompanyRepository;
use App\Repository\RoleRepository;
use App\Repository\UserRepository;
use App\Service\ImageUploadService;
use App\Service\TenantManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/{dominio}/user')]
final class UserController extends AbstractController
{
    private ImageUploadService $imageUploadService;
    private TenantManager $tenantManager;
    private UserPasswordHasherInterface $passwordHasher;

    public function __construct(ImageUploadService $imageUploadService, TenantManager $tenantManager, UserPasswordHasherInterface $passwordHasher)
    {
        $this->imageUploadService = $imageUploadService;
        $this->tenantManager = $tenantManager;
        $this->passwordHasher = $passwordHasher;
    }

    #[Route('', name: 'app_user_index', methods: ['GET'])]
    public function index(string $dominio, UserRepository $userRepository): Response
    {
        if (empty($dominio)) {
            throw $this->createNotFoundException('Dominio no especificado en la ruta.');
        }
        $this->tenantManager->setCurrentTenant($dominio);

        $em = $this->tenantManager->getEntityManager();

        $userRole = $em->getRepository(Role::class)->findOneBy(['name' => 'ROLE_USER']);

        $users = $userRepository->findBy([
            'status' => Status::ACTIVE,
            'role' => $userRole,
        ]);
        return $this->render('user/index.html.twig', [
            'users' => $users,
            'dominio' => $dominio,
        ]);
    }

    #[Route('/new', name: 'app_user_new', methods: ['GET', 'POST'])]
    public function new(string $dominio, Request $request, EntityManagerInterface $entityManager): Response
    {

        if (empty($dominio)) {
            throw $this->createNotFoundException('Dominio no especificado en la ruta.');
        }

        error_log('Valor de $dominio recibido en new: ' . var_export($dominio, true));
        $this->tenantManager->setCurrentTenant($dominio);
        $user = new User();
        $user->setStatus(Status::INACTIVE); // Usuario inactivo por defecto (status = 0)
        $user->setCreatedAt(new \DateTimeImmutable());
        $user->setUpdatedAt(new \DateTimeImmutable());

        $form = $this->createForm(UserType::class, $user, ['dominio' => $dominio]);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            // Manejar el password si se proporcionó
            $plainPassword = $form->get('password')->getData();
            if ($plainPassword) {
                $hashedPassword = $this->passwordHasher->hashPassword($user, $plainPassword);
                $user->setPassword($hashedPassword);
            }
            $user->setStatus(Status::ACTIVE);
            $user->setVerified(false);

            $entityManager->persist($user);
            $entityManager->flush();
            return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
        }
        return $this->render('user/new.html.twig', [
            'user' => $user,
            'form' => $form,
            'dominio' => $dominio, // <-- Asegura que la variable dominio esté disponible en la vista
        ]);
    }

    #[Route('/{id}', name: 'app_user_show', methods: ['GET'])]
    public function show(string $dominio, User $user): Response
    {
        if (empty($dominio)) {
            throw $this->createNotFoundException('Dominio no especificado en la ruta.');
        }
        try {
            $this->tenantManager->setCurrentTenant($dominio);

            return $this->render('user/show.html.twig', [
                'user' => $user,
                'dominio' => $dominio,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}/edit', name: 'app_user_edit', methods: ['GET', 'POST'])]
    public function edit(string $dominio, Request $request, User $user, EntityManagerInterface $entityManager): Response
    {
        if (empty($dominio)) {
            throw $this->createNotFoundException('Dominio no especificado en la ruta.');
        }
        try {
            $this->tenantManager->setCurrentTenant($dominio);

            $form = $this->createForm(UserType::class, $user, [
                'dominio' => $dominio,
            ]);

            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                // Manejar el password si se proporcionó
                $plainPassword = $form->get('password')->getData();
                if ($plainPassword) {
                    $hashedPassword = $this->passwordHasher->hashPassword($user, $plainPassword);
                    $user->setPassword($hashedPassword);
                }

                $entityManager->flush();

                return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
            }

            return $this->render('user/edit.html.twig', [
                'user' => $user,
                'form' => $form,
                'dominio' => $dominio,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}', name: 'app_user_delete', methods: ['POST'])]
    public function delete(string $dominio, Request $request, User $user, EntityManagerInterface $entityManager): Response
    {
        if (empty($dominio)) {
            throw $this->createNotFoundException('Dominio no especificado en la ruta.');
        }
        try {
            $this->tenantManager->setCurrentTenant($dominio);

            if ($this->isCsrfTokenValid('delete'.$user->getId(), $request->request->get('_token'))) {
                $user->setStatus(Status::INACTIVE);
                $entityManager->flush();
            }

            return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/download-template', name: 'app_user_download_template', priority: 10)]
    public function downloadTemplate(): StreamedResponse
    {
        try {
            $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->fromArray([
                ['NOMBRE', 'APELLIDOS', 'EMPRESA', 'FECHA DE NACIMIENTO', 'TELÉFONO', 'CORREO ELECTRÓNICO', 'N° DE EMPLEADO', 'CURP', 'GENERO', 'EDUCACIÓN']
            ], null, 'A1');

            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);

            $response = new StreamedResponse(function () use ($writer) {
                $writer->save('php://output');
            });

            $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            $response->headers->set('Content-Disposition', 'attachment;filename="plantilla_usuarios.xlsx"');

            return $response;
        } catch (\Exception $e) {
            // Log the error
            error_log('Error generating Excel template: ' . $e->getMessage());

            // Create a simple CSV file as fallback
            $response = new StreamedResponse(function () {
                $output = fopen('php://output', 'w');
                fputcsv($output, ['NOMBRE', 'APELLIDOS', 'EMPRESA', 'FECHA DE NACIMIENTO', 'TELÉFONO', 'CORREO ELECTRÓNICO', 'N° DE EMPLEADO', 'CURP', 'GENERO', 'EDUCACIÓN']);
                fclose($output);
            });

            $response->headers->set('Content-Type', 'text/csv');
            $response->headers->set('Content-Disposition', 'attachment;filename="plantilla_usuarios.csv"');

            return $response;
        }
    }

    #[Route('/bulk-upload', name: 'app_user_bulk_upload', methods: ['POST'], priority: 10)]
    public function bulkUpload(Request $request, EntityManagerInterface $em, CompanyRepository $companyRepo, RoleRepository $roleRepo): RedirectResponse
    {
        $dominio = $request->attributes->get('dominio');
        if (empty($dominio)) {
            throw $this->createNotFoundException('Dominio no especificado en la ruta.');
        }
        try {
            $this->tenantManager->setCurrentTenant($dominio);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Tenant no encontrado o inválido: ' . $dominio);
            return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
        }
        try {
            $file = $request->files->get('excel_file');
            if (!$file) {
                $this->addFlash('error', 'No se ha subido ningún archivo.');
                return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
            }

            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file->getPathname());
            $sheet = $spreadsheet->getActiveSheet();
            $allRows = $sheet->toArray();

            // Get the header row
            $headerRow = $allRows[0] ?? [];

            // Filter out completely empty rows and keep track of original row numbers
            $rows = [$headerRow]; // Start with header row
            $rowMapping = [0]; // Maps filtered row index to original row index

            foreach (array_slice($allRows, 1) as $index => $row) {
                $hasData = false;
                foreach ($row as $cell) {
                    if (!empty(trim((string)$cell))) {
                        $hasData = true;
                        break;
                    }
                }
                if ($hasData) {
                    $rows[] = $row;
                    $rowMapping[] = $index + 1; // +1 because we skipped the header row
                }
            }

            if (count($rows) <= 1) {
                $this->addFlash('error', 'El archivo no contiene datos para importar.');
                return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
            }

            $defaultCompany = $companyRepo->findOneBy([]);
            // Get role with ID 1
            //$roleUser = $roleRepo->find(1);
            $roleUser = $roleRepo->findOneBy(['name' => 'ROLE_USER']);
            if (!$roleUser) {
                // Fallback to ROLE_USER if role with ID 1 doesn't exist
                $roleUser = $roleRepo->find(1);
                if (!$roleUser) {
                    $this->addFlash('error', 'No se encontró el rol necesario para los usuarios.');
                    return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
                }
            }

            $usersAdded = 0;
            $errors = [];

            foreach (array_slice($rows, 1) as $index => $row) {
                $originalRowNumber = $rowMapping[$index + 1] + 1; // +1 for header row
                $rowNumber = $originalRowNumber + 1; // +1 to convert to Excel row number (1-based)

                if (count($row) < 10) {
                    $errors[] = "Fila $rowNumber: No tiene suficientes columnas.";
                    continue;
                }

                // New column order based on the template
                // NOMBRE, APELLIDOS, EMPRESA, FECHA DE NACIMIENTO, TELÉFONO, CORREO ELECTRÓNICO, N° DE EMPLEADO, CURP, GENERO, EDUCACIÓN
                [$nombre, $apellidos, $empresa, $fechaNacimiento, $telefono, $email, $numEmpleado, $curp, $genero, $educacion] = $row;

                // We've already filtered out completely empty rows, so we don't need to check again

                // 🚫 Validación para evitar insertar filas con datos insuficientes
                if (
                    empty(trim((string)$nombre)) ||
                    empty(trim((string)$apellidos))
                ) {
                    $errors[] = "Fila $rowNumber: Datos insuficientes. Se requiere al menos nombre y apellidos.";
                    continue;
                }

                $company = $companyRepo->findOneBy(['name' => $empresa]) ?: $defaultCompany;

                if (!$company) {
                    $errors[] = "Fila $rowNumber: No se encontró la empresa '$empresa' y no hay empresa por defecto.";
                    continue;
                }

                try {
                    // Validate required fields
                    if (empty(trim((string)$nombre))) {
                        $errors[] = "Fila $rowNumber: El nombre es obligatorio.";
                        continue;
                    }

                    if (empty(trim((string)$apellidos))) {
                        $errors[] = "Fila $rowNumber: Los apellidos son obligatorios.";
                        continue;
                    }

                    $user = new User();
                    $user->setName((string)($nombre ?? ''));
                    $user->setLastName((string)($apellidos ?? ''));

                    // Email is used as user identifier, so ensure it's set
                    $emailValue = (string)($email ?? '');
                    if (!empty(trim($emailValue))) {
                        // Verifica si ya existe ese correo
                        $existingUser = $em->getRepository(User::class)->findOneBy(['email' => $emailValue]);
                        if ($existingUser) {
                            $errors[] = "Fila $rowNumber: Ya existe un usuario con el correo '$emailValue'.";
                            continue;
                        }
                        $user->setEmail($emailValue);
                    } else {
                        $user->setEmail('user_' . uniqid() . '@placeholder.com');
                    }


                    $user->setPhoneNumber((string)($telefono ?? ''));
                    $user->setCurp((string)($curp ?? ''));
                    $user->setEmployeeNumber((string)($numEmpleado ?? ''));
                    $user->setGender((string)($genero ?? ''));
                    $user->setEducation((string)($educacion ?? ''));

                    // Company is nullable in the entity, but we've already checked it exists
                    $user->setCompany($company);

                    // Role is required (non-nullable), ensure it's set
                    if (!$roleUser) {
                        $errors[] = "Fila $rowNumber: No se pudo asignar un rol al usuario.";
                        continue;
                    }
                    $user->setRole($roleUser);

                    if ($fechaNacimiento) {
                        try {
                            $fecha = \DateTime::createFromFormat('Y-m-d', $fechaNacimiento)
                                ?: \DateTime::createFromFormat('d/m/Y', $fechaNacimiento)
                                    ?: new \DateTime($fechaNacimiento);
                            if ($fecha instanceof \DateTime) {
                                $user->setBirthday($fecha);
                            }
                        } catch (\Exception $e) {
                            $errors[] = "Fila $rowNumber: Error en formato de fecha: " . $e->getMessage();
                            // Continue with the user creation even if the date is invalid
                        }
                    }

                    // Set status to ACTIVE (1) by default
                    $user->setStatus(\App\Enum\Status::ACTIVE);

                    // These fields are required
                    $user->setCreatedAt(new \DateTimeImmutable());
                    $user->setUpdatedAt(new \DateTimeImmutable());
                } catch (\Exception $e) {
                    $errors[] = "Fila $rowNumber: Error al crear el usuario: " . $e->getMessage();
                    continue;
                }

                try {
                    $em->persist($user);
                    $usersAdded++;
                } catch (\Exception $e) {
                    $errors[] = "Fila $rowNumber: Error al persistir usuario: " . $e->getMessage();
                }
            }

            try {
                // Verify database connection before flush
                if (!$em->getConnection()->isConnected()) {
                    $em->getConnection()->connect();
                }

                if ($usersAdded > 0) {
                    $em->flush();
                    $totalRows = count($allRows) - 1; // Exclude header row
                    $processedRows = count($rows) - 1; // Exclude header row
                    $skippedRows = $totalRows - $processedRows;

                    $this->addFlash('success', "Se han importado $usersAdded usuarios correctamente.");

                } else {
                    $this->addFlash('warning', "No se importaron usuarios. Verifique los datos del archivo.");
                }

                if (!empty($errors)) {
                    $this->addFlash('warning', "Se encontraron algunos errores durante la importación:");
                    foreach ($errors as $error) {
                        $this->addFlash('warning', $error);
                    }
                }
            } catch (\Doctrine\DBAL\Exception\ConnectionException $e) {
                $this->addFlash('error', 'Error de conexión a la base de datos: ' . $e->getMessage());
                error_log('Error de conexión a la base de datos: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            } catch (\Doctrine\DBAL\Exception\UniqueConstraintViolationException $e) {
                $this->addFlash('error', 'Error: Algunos usuarios tienen datos duplicados (email, CURP, etc.): ' . $e->getMessage());
                error_log('Error de restricción única: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            } catch (\Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException $e) {
                $this->addFlash('error', 'Error: Problema con las relaciones entre entidades: ' . $e->getMessage());
                error_log('Error de clave foránea: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            } catch (\Doctrine\DBAL\Exception\NotNullConstraintViolationException $e) {
                $this->addFlash('error', 'Error: Algunos campos obligatorios están vacíos: ' . $e->getMessage());
                error_log('Error de restricción NOT NULL: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error al guardar los usuarios en la base de datos: ' . $e->getMessage());
                error_log('Error al guardar usuarios en la base de datos: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error al procesar el archivo Excel: ' . $e->getMessage());
            error_log('Error processing Excel file: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
        }

        return $this->redirectToRoute('app_user_index', ['dominio' => $dominio]);
    }
}
